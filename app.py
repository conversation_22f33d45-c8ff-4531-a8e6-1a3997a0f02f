from flask import Flask, render_template, request, jsonify, send_from_directory
import pandas as pd
import json
import os
from werkzeug.utils import secure_filename
import numpy as np

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global variable to store current dataset
current_data = None

@app.route('/')
def index():
    """Main page with chart interface"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and data processing with enhanced validation"""
    global current_data

    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type. Please upload CSV, XLSX, or XLS files.'}), 400

    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

    try:
        file.save(filepath)

        # Read the file based on extension
        if filename.lower().endswith('.csv'):
            # Try different encodings for CSV files
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None
            for encoding in encodings:
                try:
                    df = pd.read_csv(filepath, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            if df is None:
                return jsonify({'error': 'Unable to read CSV file. Please check file encoding.'}), 400

        elif filename.lower().endswith(('.xlsx', '.xls')):
            df = pd.read_excel(filepath)
        else:
            return jsonify({'error': 'Unsupported file format'}), 400

        # Validate data
        if df.empty:
            return jsonify({'error': 'The uploaded file is empty'}), 400

        if len(df.columns) == 0:
            return jsonify({'error': 'No columns found in the file'}), 400

        # Clean column names
        df.columns = df.columns.astype(str).str.strip()

        # Store data globally
        current_data = df

        # Get column information
        columns = df.columns.tolist()
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

        if len(numeric_columns) == 0:
            return jsonify({'error': 'No numeric columns found in the file. Please ensure your data contains numeric values.'}), 400

        # Clean up the file after processing
        try:
            os.remove(filepath)
        except:
            pass  # Ignore cleanup errors

        return jsonify({
            'success': True,
            'columns': columns,
            'numeric_columns': numeric_columns,
            'rows': len(df),
            'preview': df.head(10).to_dict('records'),
            'file_info': {
                'name': file.filename,
                'size': len(file.read()),
                'numeric_columns_count': len(numeric_columns),
                'total_columns_count': len(columns)
            }
        })

    except pd.errors.EmptyDataError:
        return jsonify({'error': 'The file appears to be empty or corrupted'}), 400
    except pd.errors.ParserError as e:
        return jsonify({'error': f'Error parsing file: {str(e)}'}), 400
    except Exception as e:
        return jsonify({'error': f'Error processing file: {str(e)}'}), 400

@app.route('/get_column_data/<column_name>')
def get_column_data(column_name):
    """Get data for a specific column"""
    global current_data
    
    if current_data is None:
        return jsonify({'error': 'No data loaded'}), 400
    
    if column_name not in current_data.columns:
        return jsonify({'error': 'Column not found'}), 400
    
    # Get the column data and create points
    column_data = current_data[column_name].dropna()
    
    # Create data points with index as x-axis and column values as y-axis
    points = []
    for i, value in enumerate(column_data):
        if pd.isna(value):
            continue
        points.append({
            'x': i,
            'y': float(value) if isinstance(value, (int, float)) else 0
        })
    
    return jsonify({
        'data': points,
        'column': column_name,
        'min_value': float(column_data.min()) if len(points) > 0 else 0,
        'max_value': float(column_data.max()) if len(points) > 0 else 0
    })

@app.route('/get_all_columns_data')
def get_all_columns_data():
    """Get data for all numeric columns"""
    global current_data
    
    if current_data is None:
        return jsonify({'error': 'No data loaded'}), 400
    
    numeric_columns = current_data.select_dtypes(include=[np.number]).columns.tolist()
    all_data = {}
    
    for column in numeric_columns:
        column_data = current_data[column].dropna()
        points = []
        for i, value in enumerate(column_data):
            if pd.isna(value):
                continue
            points.append({
                'x': i,
                'y': float(value)
            })
        all_data[column] = points
    
    return jsonify(all_data)

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
