class ChartAnimator {
    constructor() {
        this.chart = null;
        this.currentData = null;
        this.allColumnsData = {};
        this.selectedColumns = [];
        this.currentColumnIndex = 0;
        this.isPlaying = false;
        this.animationSpeed = 2000;
        this.transitionSpeed = 1500;
        this.animationType = 'easeInOutQuart';
        this.entryAnimation = 'sequential';

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // File upload
        document.getElementById('uploadBtn').addEventListener('click', () => this.uploadFile());
        document.getElementById('fileInput').addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                document.getElementById('uploadBtn').disabled = false;
            }
        });

        // Animation controls
        document.getElementById('startAnimation').addEventListener('click', () => this.startAnimation());
        document.getElementById('playPause').addEventListener('click', () => this.togglePlayPause());
        document.getElementById('reset').addEventListener('click', () => this.resetAnimation());
        document.getElementById('nextColumn').addEventListener('click', () => this.nextColumn());

        // Settings
        document.getElementById('animationType').addEventListener('change', (e) => {
            this.animationType = e.target.value;
        });

        document.getElementById('entryAnimation').addEventListener('change', (e) => {
            this.entryAnimation = e.target.value;
        });

        document.getElementById('animationSpeed').addEventListener('input', (e) => {
            this.animationSpeed = parseInt(e.target.value);
            document.getElementById('speedValue').textContent = e.target.value + 'ms';
        });

        document.getElementById('transitionSpeed').addEventListener('input', (e) => {
            this.transitionSpeed = parseInt(e.target.value);
            document.getElementById('transitionSpeedValue').textContent = e.target.value + 'ms';
        });
    }

    async uploadFile() {
        const fileInput = document.getElementById('fileInput');
        const file = fileInput.files[0];
        
        if (!file) {
            this.showStatus('Please select a file', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        this.showStatus('Uploading...', 'info');

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showStatus(`File uploaded successfully! ${result.rows} rows loaded.`, 'success');
                this.displayColumns(result.numeric_columns);
                this.displayDataPreview(result.preview);
                document.getElementById('columnCard').style.display = 'block';
                document.getElementById('previewCard').style.display = 'block';
            } else {
                this.showStatus(result.error, 'error');
            }
        } catch (error) {
            this.showStatus('Upload failed: ' + error.message, 'error');
        }
    }

    displayColumns(columns) {
        const columnList = document.getElementById('columnList');
        columnList.innerHTML = '';

        columns.forEach(column => {
            const div = document.createElement('div');
            div.className = 'column-checkbox';
            div.innerHTML = `
                <input type="checkbox" id="col_${column}" value="${column}">
                <label for="col_${column}">${column}</label>
            `;
            
            const checkbox = div.querySelector('input');
            checkbox.addEventListener('change', () => this.updateSelectedColumns());
            
            columnList.appendChild(div);
        });
    }

    updateSelectedColumns() {
        const checkboxes = document.querySelectorAll('#columnList input[type="checkbox"]');
        this.selectedColumns = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        document.getElementById('startAnimation').disabled = this.selectedColumns.length === 0;

        // Update visual selection
        checkboxes.forEach(cb => {
            const parent = cb.closest('.column-checkbox');
            if (cb.checked) {
                parent.classList.add('selected');
            } else {
                parent.classList.remove('selected');
            }
        });
    }

    displayDataPreview(preview) {
        const previewDiv = document.getElementById('dataPreview');
        
        if (preview.length === 0) {
            previewDiv.innerHTML = '<p>No data to preview</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'table table-sm';
        
        // Header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        Object.keys(preview[0]).forEach(key => {
            const th = document.createElement('th');
            th.textContent = key;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Body
        const tbody = document.createElement('tbody');
        preview.slice(0, 5).forEach(row => {
            const tr = document.createElement('tr');
            Object.values(row).forEach(value => {
                const td = document.createElement('td');
                td.textContent = value;
                tr.appendChild(td);
            });
            tbody.appendChild(tr);
        });
        table.appendChild(tbody);

        previewDiv.innerHTML = '<div class="data-preview-table"></div>';
        previewDiv.querySelector('.data-preview-table').appendChild(table);
    }

    async startAnimation() {
        if (this.selectedColumns.length === 0) return;

        try {
            // Load all column data
            const response = await fetch('/get_all_columns_data');
            this.allColumnsData = await response.json();

            this.currentColumnIndex = 0;
            this.isPlaying = true;
            
            document.getElementById('controlsCard').style.display = 'block';
            document.getElementById('playPause').textContent = 'Pause';
            
            this.initializeChart();
            this.animateToColumn(this.selectedColumns[0]);
            
        } catch (error) {
            this.showStatus('Error loading data: ' + error.message, 'error');
        }
    }

    initializeChart() {
        const ctx = document.getElementById('animatedChart').getContext('2d');
        
        if (this.chart) {
            this.chart.destroy();
        }

        this.chart = new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Data Points',
                    data: [],
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: this.animationSpeed,
                    easing: this.animationType
                },
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: 'Index'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Value'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Point ${context.parsed.x}: ${context.parsed.y}`;
                            }
                        }
                    }
                }
            }
        });
    }

    animateToColumn(columnName) {
        if (!this.allColumnsData[columnName]) {
            this.showStatus(`No data found for column: ${columnName}`, 'error');
            return;
        }

        const data = this.allColumnsData[columnName];
        this.currentData = data;

        // Update chart info
        document.getElementById('currentColumn').textContent = `Column: ${columnName}`;
        document.getElementById('chartStats').textContent =
            `${data.length} points | Min: ${Math.min(...data.map(p => p.y)).toFixed(2)} | Max: ${Math.max(...data.map(p => p.y)).toFixed(2)}`;

        // Create smooth transition animation
        if (this.chart.data.datasets[0].data.length > 0) {
            this.animateTransition(data, columnName);
        } else {
            this.animateFromEmpty(data, columnName);
        }
    }

    animateFromEmpty(data, columnName) {
        // Animate from nothing to full data
        this.chart.data.datasets[0].data = [];
        this.chart.data.datasets[0].label = columnName;
        this.chart.update('none');

        switch (this.entryAnimation) {
            case 'sequential':
                this.animateSequential(data);
                break;
            case 'simultaneous':
                this.animateSimultaneous(data);
                break;
            case 'wave':
                this.animateWave(data);
                break;
            case 'spiral':
                this.animateSpiral(data);
                break;
            case 'random':
                this.animateRandom(data);
                break;
            default:
                this.animateSequential(data);
        }
    }

    animateSequential(data) {
        let pointIndex = 0;
        const addPoint = () => {
            if (pointIndex < data.length && this.isPlaying) {
                this.chart.data.datasets[0].data.push(data[pointIndex]);
                this.chart.options.animation.duration = 200;
                this.chart.update('active');
                pointIndex++;
                setTimeout(addPoint, 50);
            }
        };
        setTimeout(addPoint, 100);
    }

    animateSimultaneous(data) {
        this.chart.data.datasets[0].data = data;
        this.chart.options.animation.duration = this.animationSpeed;
        this.chart.options.animation.easing = this.animationType;
        this.chart.update('active');
    }

    animateWave(data) {
        const sortedData = [...data].sort((a, b) => a.x - b.x);
        let pointIndex = 0;
        const addPoint = () => {
            if (pointIndex < sortedData.length && this.isPlaying) {
                this.chart.data.datasets[0].data.push(sortedData[pointIndex]);
                this.chart.options.animation.duration = 150;
                this.chart.update('active');
                pointIndex++;
                setTimeout(addPoint, 30);
            }
        };
        setTimeout(addPoint, 100);
    }

    animateSpiral(data) {
        // Sort points by distance from center
        const centerX = data.reduce((sum, p) => sum + p.x, 0) / data.length;
        const centerY = data.reduce((sum, p) => sum + p.y, 0) / data.length;

        const sortedData = [...data].sort((a, b) => {
            const distA = Math.sqrt((a.x - centerX) ** 2 + (a.y - centerY) ** 2);
            const distB = Math.sqrt((b.x - centerX) ** 2 + (b.y - centerY) ** 2);
            return distA - distB;
        });

        let pointIndex = 0;
        const addPoint = () => {
            if (pointIndex < sortedData.length && this.isPlaying) {
                this.chart.data.datasets[0].data.push(sortedData[pointIndex]);
                this.chart.options.animation.duration = 100;
                this.chart.update('active');
                pointIndex++;
                setTimeout(addPoint, 40);
            }
        };
        setTimeout(addPoint, 100);
    }

    animateRandom(data) {
        const shuffledData = [...data].sort(() => Math.random() - 0.5);
        let pointIndex = 0;
        const addPoint = () => {
            if (pointIndex < shuffledData.length && this.isPlaying) {
                this.chart.data.datasets[0].data.push(shuffledData[pointIndex]);
                this.chart.options.animation.duration = 100;
                this.chart.update('active');
                pointIndex++;
                setTimeout(addPoint, 60);
            }
        };
        setTimeout(addPoint, 100);
    }

    animateTransition(newData, columnName) {
        // Enhanced smooth transition between columns with multiple effects
        const oldData = [...this.chart.data.datasets[0].data];
        const maxLength = Math.max(oldData.length, newData.length);

        // Create interpolated frames with enhanced easing
        const frames = Math.floor(this.transitionSpeed / 16); // 60fps
        let currentFrame = 0;

        // Add color transition
        const startColor = { r: 54, g: 162, b: 235 };
        const endColor = { r: 255, g: 99, b: 132 };

        const animate = () => {
            if (currentFrame <= frames && this.isPlaying) {
                const progress = currentFrame / frames;
                const easedProgress = this.getEasedProgress(progress);

                const interpolatedData = [];
                for (let i = 0; i < maxLength; i++) {
                    const oldPoint = oldData[i] || { x: i, y: 0 };
                    const newPoint = newData[i] || { x: i, y: 0 };

                    // Add slight randomness for organic movement
                    const randomOffset = (Math.sin(currentFrame * 0.1 + i) * 0.5) * (1 - easedProgress);

                    interpolatedData.push({
                        x: oldPoint.x + (newPoint.x - oldPoint.x) * easedProgress,
                        y: oldPoint.y + (newPoint.y - oldPoint.y) * easedProgress + randomOffset
                    });
                }

                // Interpolate colors
                const r = Math.round(startColor.r + (endColor.r - startColor.r) * easedProgress);
                const g = Math.round(startColor.g + (endColor.g - startColor.g) * easedProgress);
                const b = Math.round(startColor.b + (endColor.b - startColor.b) * easedProgress);

                this.chart.data.datasets[0].data = interpolatedData.slice(0, newData.length);
                this.chart.data.datasets[0].label = columnName;
                this.chart.data.datasets[0].backgroundColor = `rgba(${r}, ${g}, ${b}, 0.6)`;
                this.chart.data.datasets[0].borderColor = `rgba(${r}, ${g}, ${b}, 1)`;
                this.chart.options.animation.duration = 0;
                this.chart.update('none');

                currentFrame++;
                requestAnimationFrame(animate);
            } else if (currentFrame > frames) {
                // Ensure final state is exact
                this.chart.data.datasets[0].data = newData;
                this.chart.update('none');
            }
        };

        animate();
    }

    getEasedProgress(t) {
        // Multiple easing functions based on animation type
        switch (this.animationType) {
            case 'easeInOutQuart':
                return t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2;
            case 'easeInBounce':
                return 1 - this.bounceOut(1 - t);
            case 'easeOutBounce':
                return this.bounceOut(t);
            case 'easeInOutElastic':
                const c5 = (2 * Math.PI) / 4.5;
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5
                    ? -(Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * c5)) / 2
                    : (Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * c5)) / 2 + 1;
            case 'easeInOutBack':
                const c1 = 1.70158;
                const c2 = c1 * 1.525;
                return t < 0.5
                    ? (Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
                    : (Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2;
            case 'easeInOutCirc':
                return t < 0.5
                    ? (1 - Math.sqrt(1 - Math.pow(2 * t, 2))) / 2
                    : (Math.sqrt(1 - Math.pow(-2 * t + 2, 2)) + 1) / 2;
            case 'easeInOutExpo':
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5
                    ? Math.pow(2, 20 * t - 10) / 2
                    : (2 - Math.pow(2, -20 * t + 10)) / 2;
            case 'linear':
            default:
                return t;
        }
    }

    bounceOut(t) {
        const n1 = 7.5625;
        const d1 = 2.75;

        if (t < 1 / d1) {
            return n1 * t * t;
        } else if (t < 2 / d1) {
            return n1 * (t -= 1.5 / d1) * t + 0.75;
        } else if (t < 2.5 / d1) {
            return n1 * (t -= 2.25 / d1) * t + 0.9375;
        } else {
            return n1 * (t -= 2.625 / d1) * t + 0.984375;
        }
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    nextColumn() {
        if (this.selectedColumns.length === 0) return;

        this.currentColumnIndex = (this.currentColumnIndex + 1) % this.selectedColumns.length;
        const nextColumn = this.selectedColumns[this.currentColumnIndex];
        
        // Use transition speed for column changes
        this.chart.options.animation.duration = this.transitionSpeed;
        this.animateToColumn(nextColumn);
    }

    togglePlayPause() {
        this.isPlaying = !this.isPlaying;
        document.getElementById('playPause').textContent = this.isPlaying ? 'Pause' : 'Play';
        
        if (this.isPlaying && this.selectedColumns.length > 1) {
            this.startAutoTransition();
        } else {
            this.stopAutoTransition();
        }
    }

    startAutoTransition() {
        if (this.autoTransitionInterval) {
            clearInterval(this.autoTransitionInterval);
        }

        this.autoTransitionInterval = setInterval(() => {
            if (this.isPlaying) {
                this.nextColumn();
            }
        }, this.animationSpeed + this.transitionSpeed + 1000);
    }

    stopAutoTransition() {
        if (this.autoTransitionInterval) {
            clearInterval(this.autoTransitionInterval);
            this.autoTransitionInterval = null;
        }
    }

    resetAnimation() {
        this.currentColumnIndex = 0;
        this.isPlaying = false;
        this.stopAutoTransition();
        
        document.getElementById('playPause').textContent = 'Play';
        
        if (this.selectedColumns.length > 0) {
            this.animateToColumn(this.selectedColumns[0]);
        }
    }

    showStatus(message, type) {
        const statusDiv = document.getElementById('uploadStatus');
        statusDiv.textContent = message;
        statusDiv.className = type;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new ChartAnimator();
});
