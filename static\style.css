body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar {
    background-color: #343a40;
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.sidebar h2 {
    color: #fff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.sidebar .card {
    background-color: #495057;
    border: none;
    color: white;
}

.sidebar .card-header {
    background-color: #007bff;
    border-bottom: none;
    color: white;
}

.sidebar .card-body {
    background-color: #495057;
}

.main-content {
    padding: 20px;
}

.chart-container {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-height: 600px;
}

#animatedChart {
    max-height: 500px;
    width: 100% !important;
}

.column-checkbox {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #6c757d;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.column-checkbox:hover {
    background-color: #5a6268;
}

.column-checkbox input[type="checkbox"] {
    margin-right: 10px;
}

.column-checkbox.selected {
    background-color: #007bff;
}

#uploadStatus {
    font-size: 0.9em;
}

.success {
    color: #28a745;
}

.error {
    color: #dc3545;
}

.info {
    color: #17a2b8;
}

#chartInfo h3 {
    color: #007bff;
    margin-bottom: 10px;
}

#chartStats {
    color: #6c757d;
    font-size: 0.9em;
}

.form-range {
    margin-bottom: 5px;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.data-preview-table {
    font-size: 0.8em;
    max-height: 200px;
    overflow-y: auto;
}

.data-preview-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-preview-table th,
.data-preview-table td {
    padding: 4px 8px;
    border: 1px solid #6c757d;
    text-align: left;
}

.data-preview-table th {
    background-color: #007bff;
    color: white;
    position: sticky;
    top: 0;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    font-size: 1.2em;
    color: #6c757d;
}

.animation-progress {
    width: 100%;
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 10px;
}

.animation-progress-bar {
    height: 100%;
    background-color: #007bff;
    width: 0%;
    transition: width 0.3s ease;
}
