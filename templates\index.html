<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Chart Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Toggle Button -->
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <h2 class="mb-4">Chart Animation</h2>
                
                <!-- File Upload -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Upload Data</h5>
                    </div>
                    <div class="card-body">
                        <input type="file" id="fileInput" class="form-control mb-3" accept=".csv,.xlsx,.xls">
                        <button id="uploadBtn" class="btn btn-primary w-100">Upload File</button>
                        <div id="uploadStatus" class="mt-2"></div>
                    </div>
                </div>

                <!-- Column Selection -->
                <div class="card mb-4" id="columnCard" style="display: none;">
                    <div class="card-header">
                        <h5>Select Columns</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="xAxisColumn" class="form-label">X-Axis Column:</label>
                            <select id="xAxisColumn" class="form-select">
                                <option value="index">Use Index (Default)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Y-Axis Columns:</label>
                            <div id="columnList"></div>
                        </div>
                        <button id="startAnimation" class="btn btn-success w-100 mt-3" disabled>Start Animation</button>
                    </div>
                </div>

                <!-- Animation Controls -->
                <div class="card mb-4" id="controlsCard" style="display: none;">
                    <div class="card-header">
                        <h5>Animation Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="animationType" class="form-label">Animation Type:</label>
                            <select id="animationType" class="form-select">
                                <option value="easeInOutQuart">Smooth</option>
                                <option value="easeInBounce">Bounce In</option>
                                <option value="easeOutBounce">Bounce Out</option>
                                <option value="easeInOutElastic">Elastic</option>
                                <option value="easeInOutBack">Back</option>
                                <option value="easeInOutCirc">Circular</option>
                                <option value="easeInOutExpo">Exponential</option>
                                <option value="linear">Linear</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="entryAnimation" class="form-label">Entry Animation:</label>
                            <select id="entryAnimation" class="form-select">
                                <option value="sequential">Sequential</option>
                                <option value="simultaneous">Simultaneous</option>
                                <option value="wave">Wave</option>
                                <option value="spiral">Spiral</option>
                                <option value="random">Random</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="animationSpeed" class="form-label">Speed (ms):</label>
                            <input type="range" id="animationSpeed" class="form-range" min="500" max="5000" value="2000">
                            <span id="speedValue">2000ms</span>
                        </div>
                        <div class="mb-3">
                            <label for="transitionSpeed" class="form-label">Transition Speed (ms):</label>
                            <input type="range" id="transitionSpeed" class="form-range" min="500" max="3000" value="1500">
                            <span id="transitionSpeedValue">1500ms</span>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fixedAxisValues">
                                <label class="form-check-label" for="fixedAxisValues">
                                    Fixed Axis Values
                                </label>
                            </div>
                            <small class="form-text text-muted">Keep axis ranges consistent across all columns</small>
                        </div>
                        <button id="playPause" class="btn btn-warning w-100 mb-2">Pause</button>
                        <button id="reset" class="btn btn-secondary w-100 mb-2">Reset</button>
                        <button id="nextColumn" class="btn btn-info w-100">Next Column</button>
                    </div>
                </div>

                <!-- Data Preview -->
                <div class="card" id="previewCard" style="display: none;">
                    <div class="card-header">
                        <h5>Data Preview</h5>
                    </div>
                    <div class="card-body">
                        <div id="dataPreview"></div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Chart Area -->
        <div class="main-content">
            <div class="chart-container">
                <div id="chartInfo" class="mb-3">
                    <h3 id="currentColumn">No data loaded</h3>
                    <p id="chartStats"></p>
                </div>
                <canvas id="animatedChart"></canvas>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='chart-animation.js') }}"></script>
</body>
</html>
